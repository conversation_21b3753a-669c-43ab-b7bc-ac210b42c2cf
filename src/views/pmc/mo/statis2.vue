<template>
  <div class="p-4">
    <!-- 搜索卡片 -->
    <el-card class="mb-6 shadow-md">
      <template #header>
        <div class="flex justify-between items-center">
          <h3 class="text-lg font-semibold">条件搜索</h3>
        </div>
      </template>

      <el-form :model="searchForm" label-width="100px" :inline="true">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="接单日期">
              <el-date-picker
                v-model="searchForm.bargainDate"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>

          <el-col :span="5">
            <el-form-item label="客户代码">
              <el-input v-model="searchForm.customerCode" clearable/>
            </el-form-item>
          </el-col>

          <el-col :span="5">
            <el-form-item label="DR单号">
              <el-input v-model="searchForm.drNumber" clearable/>
            </el-form-item>
          </el-col>

          <el-col :span="5">
            <el-form-item label="负责人">
              <el-select v-model="searchForm.merchandiser" clearable :disabled="enable">
                <el-option v-for="item in mData" :key="item" :label="item" :value="item" />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="客户要求交期">
              <el-date-picker
                v-model="searchForm.deliveryDate"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="YYYY-MM-DD"
                format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>

          <el-col :span="5">
            <el-form-item label="MO单号">
              <el-input v-model="searchForm.moNumber" clearable/>
            </el-form-item>
          </el-col>

          <el-col :span="5">
            <el-form-item label="利润中心">
              <el-select v-model="searchForm.profitCenter" clearable>
                <el-option v-for="item in profitCenters" :key="item" :label="item" :value="item" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24" class="flex justify-end">
            <el-button type="primary" :loading="loading" @click="handleQuery">
              <el-icon><Search /></el-icon>
              查询
            </el-button>
            <el-button @click="handleReset">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
            <el-button type="success" :loading="exportLoading" @click="handleExport">
              <el-icon><Download /></el-icon>
              导出Excel
            </el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 数据表格 -->
    <el-table :data="paginatedData" :loading="loading" border stripe>
      <el-table-column prop="customerCode" label="客户代码" width="100" />
      <el-table-column prop="pmcManager" label="PMC负责人" width="100" />
      <el-table-column prop="profitCenter" label="利润中心" width="100" />
      <el-table-column prop="profitCenterCount" label="利润中心统计" width="120">
        <template #default="{ row }">
          <span class="cursor-pointer text-blue-500 hover:text-blue-700" @click="showDetail(row, 'profitCenterDetails')">{{ row.profitCenterCount }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="orderCount" label="订单统计" width="100">
        <template #default="{ row }">
          <span class="cursor-pointer text-blue-500 hover:text-blue-700" @click="showDetail(row, 'orderDetails')">{{ row.orderCount }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="deliveryAchieved" label="交期达成" width="100">
        <template #default="{ row }">
          <span class="cursor-pointer text-blue-500 hover:text-blue-700" @click="showDetail(row, 'deliveryAchievedDetails')">{{ row.deliveryAchieved }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="deliveryDelayed" label="交期延误" width="100">
        <template #default="{ row }">
          <span class="cursor-pointer text-blue-500 hover:text-blue-700" @click="showDetail(row, 'deliveryDelayedDetails')">{{ row.deliveryDelayed }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="achievementRate" label="达成率" width="100"></el-table-column>
      <el-table-column prop="assemblyOrderItems" label="装配订单项数" width="120">
        <template #default="{ row }">
          <span class="cursor-pointer text-blue-500 hover:text-blue-700" @click="showDetail(row, 'assemblyOrderItemDetails')">{{ row.assemblyOrderItems }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="totalAssemblyOrders" label="装配订单总数" width="140">
        <template #default="{ row }">
          <span class="cursor-pointer text-blue-500 hover:text-blue-700" @click="showDetail(row, 'totalAssemblyOrderDetails')">{{ row.totalAssemblyOrders }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="assemblyDeliveryAchieved" label="交期达成" width="100">
        <template #default="{ row }">
          <span class="cursor-pointer text-blue-500 hover:text-blue-700" @click="showDetail(row, 'assemblyDeliveryAchievedDetails')">{{ row.assemblyDeliveryAchieved }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="assemblyDeliveryDelayed" label="交期延误" width="100">
        <template #default="{ row }">
          <span class="cursor-pointer text-blue-500 hover:text-blue-700" @click="showDetail(row, 'assemblyDeliveryDelayedDetails')">{{ row.assemblyDeliveryDelayed }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="assemblyAchievementRate" label="达成率" width="100"></el-table-column>
      <el-table-column prop="nonAssemblyOrderItems" label="非装配订单项数" width="140">
        <template #default="{ row }">
          <span class="cursor-pointer text-blue-500 hover:text-blue-700" @click="showDetail(row, 'nonAssemblyOrderItemDetails')">{{ row.nonAssemblyOrderItems }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="nonAssemblyDeliveryAchieved" label="交期达成" width="100">
        <template #default="{ row }">
          <span class="cursor-pointer text-blue-500 hover:text-blue-700" @click="showDetail(row, 'nonAssemblyDeliveryAchievedDetails')">{{ row.nonAssemblyDeliveryAchieved }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="nonAssemblyDeliveryDelayed" label="交期延误" width="100">
        <template #default="{ row }">
          <span class="cursor-pointer text-blue-500 hover:text-blue-700" @click="showDetail(row, 'nonAssemblyDeliveryDelayedDetails')">{{ row.nonAssemblyDeliveryDelayed }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="nonAssemblyAchievementRate" label="达成率" width="100">
      </el-table-column>
    </el-table>

    <!-- 详情对话框 -->
    <el-dialog v-model="detailVisible" title="详细信息" width="80%" append-to-body>
      <template #header>
        <div class="flex justify-between items-center">
          <span class="text-lg font-semibold">详细信息 (共{{ detailData.length }}条记录)</span>
          <el-button type="primary" :loading="detailExportLoading" @click="handleDetailExport">
            <el-icon><Download /></el-icon>
            下载Excel
          </el-button>
        </div>
      </template>

      <el-table :data="paginatedDetailData" border stripe>
        <el-table-column prop="status" label="状态" width="100"/>
        <el-table-column prop="moNo" label="MO号" width="100"/>
        <el-table-column prop="projectDesc" label="项目描述" width="150"/>
        <el-table-column prop="customerCode" label="客户代码" width="100"/>
        <el-table-column prop="orderQuantity" label="订单数量" width="100"/>
        <el-table-column prop="productionQuantity" label="生产数量" width="100"/>
        <el-table-column prop="shortageQuantity" label="欠货结数量" width="100"/>
        <el-table-column prop="orderDate" label="接单日期" width="100"/>
        <el-table-column prop="assemblyCompletionTime" label="装配完成时间" width="120"/>
        <el-table-column prop="customerRequiredDate" label="客户要求交期" width="120"/>
        <el-table-column prop="pmcRequiredDate" label="PMC要求交期" width="120"/>
        <el-table-column prop="remainingTime" label="末工序时间" width="100"/>
        <el-table-column prop="profitCenter" label="利润中心" width="100"/>
        <el-table-column prop="manufacturingDept" label="制造部门" width="100"/>
        <el-table-column prop="partsBomList" label="零件BOM清单" width="120"/>
        <el-table-column prop="standardPartsBomList" label="标准件BOM清单" width="120"/>
        <el-table-column prop="projectPlan" label="项目计划" width="100"/>
        <el-table-column prop="drawingNo" label="图号" width="100"/>
        <el-table-column prop="soNo" label="SO号" width="100"/>
        <el-table-column prop="soItm" label="SO项次" width="100"/>
        <el-table-column prop="version" label="版本" width="100"/>
        <el-table-column prop="warehouseDate" label="入仓日期" width="100"/>
        <el-table-column prop="delayDays" label="延误天数" width="100"/>
        <el-table-column prop="customerPoNo" label="客户PO号" width="100"/>
        <el-table-column prop="remark" label="备注" width="150"/>
        <el-table-column prop="orderManager" label="下单负责人" width="100"/>
        <el-table-column prop="merchandiser" label="跟单负责人" width="100"/>
        <el-table-column prop="purchaseManager" label="采购负责人" width="100"/>
        <el-table-column prop="powerDistributionTime" label="分电时间" width="100"/>
        <el-table-column prop="partsOrderCategory" label="零件分订单类别" width="120"/>
        <el-table-column prop="categoryDesc" label="类别描述" width="150"/>
        <el-table-column prop="planType" label="计划类型" width="100"/>
        <el-table-column prop="productNo" label="品号" width="100"/>
        <el-table-column prop="upperOrderNo" label="上层订单号" width="100"/>
        <el-table-column prop="drNo" label="DR号" width="100"/>
        <el-table-column prop="drItm" label="DR项次" width="100"/>
        <el-table-column prop="action" label="动作" width="100"/>
        <el-table-column prop="productionDrawingsTotal" label="生产图纸总数" width="120"/>
        <el-table-column prop="unfinishedProductionDrawings" label="生产未完成图纸总数" width="150"/>
        <el-table-column prop="outsourcedDrawingsTotal" label="外发图纸总数" width="120"/>
        <el-table-column prop="unfinishedOutsourcedDrawings" label="外发图纸未完成总数" width="150"/>
        <el-table-column prop="sheetMetalDrawingsTotal" label="钣金图纸总数" width="120"/>
        <el-table-column prop="unfinishedSheetMetalDrawings" label="钣金图纸未完成总数" width="150"/>
        <el-table-column prop="qaDrawingsCount" label="QA图纸数" width="100"/>
        <el-table-column prop="standardPartsTotal" label="标准件总项数" width="120"/>
        <el-table-column prop="unfinishedStandardParts" label="标准件未完成项数" width="150"/>
      </el-table>

      <!-- 详情对话框分页 -->
      <div class="mt-4 flex justify-end">
        <el-pagination
          v-model:current-page="detailCurrentPage"
          v-model:page-size="detailPageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next"
          :total="detailData.length"
          @size-change="handleDetailSizeChange"
          @current-change="handleDetailCurrentChange"
          class="shadow-sm"
        />
      </div>
    </el-dialog>

    <!-- 分页 -->
    <div class="mt-4 flex justify-end">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next"
        :total="totalItems"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        class="shadow-sm"
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, onMounted, reactive, ref } from 'vue';
import { ElDatePicker, ElFormItem, ElMessage, ElPagination } from 'element-plus';
import { Download, Refresh, Search } from '@element-plus/icons-vue';
import { getStatis2Data } from '@/api/pmc/statis2';
import { getMerchandiserList } from '@/api/pmc/merchandiser';
import { getProfitCenterList } from '@/api/pmc/statis2';
import { checkPermi } from '@/utils/permission';
import { useUserStore } from '@/store/modules/user';
import { exportToExcel } from '@/utils/excel';

// 搜索表单数据
const searchForm = reactive({
  bargainDate: [],
  deliveryDate: [],
  customerCode: '',
  drNumber: '',
  moNumber: '',
  profitCenter: '',
  merchandiser: '',
  completionStatus: ''
});

const enable = ref(false);
const mData = ref<string[]>([]);
const profitCenters = ref<string[]>([]);

// 表格数据
const tableData = ref([]);
const loading = ref(false);
const exportLoading = ref(false);

// 分页相关
const currentPage = ref(1);
const pageSize = ref(10);
const totalItems = ref(0);

// 详情对话框分页相关变量
const detailCurrentPage = ref(1);
const detailPageSize = ref(10);

// 计算分页后的数据
const paginatedData = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  return tableData.value.slice(start, end);
});

// 计算详情对话框分页后的数据
const paginatedDetailData = computed(() => {
  const start = (detailCurrentPage.value - 1) * detailPageSize.value;
  const end = start + detailPageSize.value;
  return detailData.value.slice(start, end);
});

// 详情对话框相关
const detailVisible = ref(false);
const detailExportLoading = ref(false);
const detailData = ref([]);
const currentDetailType = ref(''); // 记录当前详情类型，用于生成文件名

// 显示详情
const showDetail = (row: any, detailType: string) => {
  // 根据点击的列显示对应的详情数据
  detailData.value = row[detailType] || [];
  currentDetailType.value = detailType;
  // 重置详情对话框分页状态
  detailCurrentPage.value = 1;
  detailPageSize.value = 10;
  detailVisible.value = true;
};

// 查询方法
const handleQuery = async () => {
  loading.value = true;
  try {
    const res = await getStatis2Data(searchForm);
    tableData.value = res.data;
    totalItems.value = res.data.length;
    currentPage.value = 1;
  } catch (err: any) {
    ElMessage.error('查询失败：' + err.message);
  } finally {
    loading.value = false;
  }
};

// 重置方法
const handleReset = () => {
  searchForm.bargainDate = [];
  searchForm.deliveryDate = [];
  searchForm.customerCode = '';
  searchForm.drNumber = '';
  searchForm.moNumber = '';
  searchForm.profitCenter = '';
  searchForm.merchandiser = '';
};

// 导出方法
const handleExport = async () => {
  if (!tableData.value || tableData.value.length === 0) {
    ElMessage.warning('没有数据可以导出');
    return;
  }

  exportLoading.value = true;
  try {
    // 定义主表格的表头映射
    const mainHeaders = {
      customerCode: '客户代码',
      pmcManager: 'PMC负责人',
      profitCenter: '利润中心',
      profitCenterCount: '利润中心统计',
      orderCount: '订单统计',
      deliveryAchieved: '交期达成',
      deliveryDelayed: '交期延误',
      achievementRate: '达成率',
      assemblyOrderItems: '装配订单项数',
      totalAssemblyOrders: '装配订单总数',
      assemblyDeliveryAchieved: '装配交期达成',
      assemblyDeliveryDelayed: '装配交期延误',
      assemblyAchievementRate: '装配达成率',
      nonAssemblyOrderItems: '非装配订单项数',
      nonAssemblyDeliveryAchieved: '非装配交期达成',
      nonAssemblyDeliveryDelayed: '非装配交期延误',
      nonAssemblyAchievementRate: '非装配达成率'
    };

    const fileName = `成品订单统计_${new Date().getTime()}`;

    // 使用绿色表头（与原导出按钮颜色保持一致）
    await exportToExcel(tableData.value, mainHeaders, fileName, '成品订单统计', '67C23A');
    ElMessage.success('导出成功');
  } catch (err: any) {
    ElMessage.error('导出失败：' + err.message);
  } finally {
    exportLoading.value = false;
  }
};

// 详情导出方法
const handleDetailExport = async () => {
  if (!detailData.value || detailData.value.length === 0) {
    ElMessage.warning('没有数据可以导出');
    return;
  }

  detailExportLoading.value = true;
  try {
    // 定义表头映射
    const headers = {
      status: '状态',
      moNo: 'MO号',
      projectDesc: '项目描述',
      customerCode: '客户代码',
      orderQuantity: '订单数量',
      productionQuantity: '生产数量',
      shortageQuantity: '欠货结数量',
      orderDate: '接单日期',
      assemblyCompletionTime: '装配完成时间',
      customerRequiredDate: '客户要求交期',
      pmcRequiredDate: 'PMC要求交期',
      remainingTime: '末工序时间',
      profitCenter: '利润中心',
      manufacturingDept: '制造部门',
      partsBomList: '零件BOM清单',
      standardPartsBomList: '标准件BOM清单',
      projectPlan: '项目计划',
      drawingNo: '图号',
      soNo: 'SO号',
      soItm: 'SO项次',
      version: '版本',
      warehouseDate: '入仓日期',
      delayDays: '延误天数',
      customerPoNo: '客户PO号',
      remark: '备注',
      orderManager: '下单负责人',
      merchandiser: '跟单负责人',
      purchaseManager: '采购负责人',
      powerDistributionTime: '分电时间',
      partsOrderCategory: '零件分订单类别',
      categoryDesc: '类别描述',
      planType: '计划类型',
      productNo: '品号',
      upperOrderNo: '上层订单号',
      drNo: 'DR号',
      drItm: 'DR项次',
      action: '动作',
      productionDrawingsTotal: '生产图纸总数',
      unfinishedProductionDrawings: '生产未完成图纸总数',
      outsourcedDrawingsTotal: '外发图纸总数',
      unfinishedOutsourcedDrawings: '外发图纸未完成总数',
      sheetMetalDrawingsTotal: '钣金图纸总数',
      unfinishedSheetMetalDrawings: '钣金图纸未完成总数',
      qaDrawingsCount: 'QA图纸数',
      standardPartsTotal: '标准件总项数',
      unfinishedStandardParts: '标准件未完成项数'
    };

    // 生成文件名
    const typeNameMap: Record<string, string> = {
      profitCenterDetails: '利润中心统计',
      orderDetails: '订单统计',
      deliveryAchievedDetails: '交期达成',
      deliveryDelayedDetails: '交期延误',
      assemblyOrderItemDetails: '装配订单项数',
      totalAssemblyOrderDetails: '装配订单总数',
      assemblyDeliveryAchievedDetails: '装配交期达成',
      assemblyDeliveryDelayedDetails: '装配交期延误',
      nonAssemblyOrderItemDetails: '非装配订单项数',
      nonAssemblyDeliveryAchievedDetails: '非装配交期达成',
      nonAssemblyDeliveryDelayedDetails: '非装配交期延误'
    };

    const typeName = typeNameMap[currentDetailType.value] || '详情数据';
    const fileName = `成品订单统计_${typeName}_${new Date().getTime()}`;

    // 使用蓝色表头
    await exportToExcel(detailData.value, headers, fileName, '详情数据', '4472C4');
    ElMessage.success('导出成功');
  } catch (err: any) {
    ElMessage.error('导出失败：' + err.message);
  } finally {
    detailExportLoading.value = false;
  }
};

// 处理分页变化
const handleSizeChange = (val: number) => {
  pageSize.value = val;
  currentPage.value = 1;
};

const handleCurrentChange = (val: number) => {
  currentPage.value = val;
};

// 处理详情对话框分页变化
const handleDetailSizeChange = (val: number) => {
  detailPageSize.value = val;
  detailCurrentPage.value = 1;
};

const handleDetailCurrentChange = (val: number) => {
  detailCurrentPage.value = val;
};

// 加载负责人数据
const loadMerchandiserData = async () => {
  try {
    const res = await getMerchandiserList();
    mData.value = res.data;
  } catch (err: any) {
    ElMessage.error(err.message);
  }
};

// 加载利润中心数据
const loadProfitCenterData = async () => {
  try {
    const res = await getProfitCenterList();
    profitCenters.value = res.data;
  } catch (err: any) {
    ElMessage.error(err.message);
  }
};

// 初始化
onMounted(() => {
  loadProfitCenterData();
  if (checkPermi(['pmc:mo:statis2'])) {
    loadMerchandiserData();
  } else {
    searchForm.merchandiser = useUserStore().nickname;
    enable.value = true;
  }
});
</script>

<style scoped>
.el-date-picker {
  width: 240px;
}

.el-select {
  width: 150px;
}
.cursor-pointer {
  cursor: pointer;
}
</style>
